class RiskAnalysisItem < ApplicationRecord
  belongs_to :risk_analysis

  before_save :calculate_risk

  validates :property, :danger, :security, :probability, :vulnerability_description, :effect,
            :data_processing_impact_assessment, presence: true

  enum property: { confidentiality: 0, availability: 1, integrity: 2 }
  enum probability: { rare: 1, unlikely: 2, possible: 3, probable: 4, almost_certain: 5 }
  enum effect: { very_low: 1, low: 2, medium: 3, high: 4, very_high: 5 }, _prefix: :effect
  enum risk: { low: 0, medium: 1, high: 2, critical: 3 }, _prefix: :risk
  enum data_processing_impact_assessment: { required: 0, not_required: 1 }

  private

  def calculate_risk
    score = probability_for_database * effect_for_database

    self.risk = case score
                when 0..3 then :low
                when 4..7 then :medium
                when 8..12 then :high
                when 13..25 then :critical
                else
                  raise "Unexpected risk score: #{score}"
                end
  end
end
