module Api
  module V1
    class RiskAnalysesController < ApiController
      before_action :set_risk_analysis, except: %i[index create]
      before_action :authorize_risk_analysis
      before_action :skip_policy_scope

      def index
        results = search(scope: RiskAnalysis.all, filters: params[:f]).results
        @risk_analyses = paginate(authorize!(results))
      end

      def show; end

      def create
        @risk_analysis = RiskAnalysis.new(risk_analysis_params)
        if @risk_analysis.save
          respond_with @risk_analysis
        else
          render json: { errors: @risk_analysis.errors.messages }, status: :unprocessable_entity
        end
      end

      def update
        if @risk_analysis.update(risk_analysis_params)
          respond_with @risk_analysis
        else
          render json: { errors: @risk_analysis.errors.messages }, status: :unprocessable_entity
        end
      end

      def destroy
        @risk_analysis.destroy
        head :no_content
      end

      def create_risk_analysis_item
        @risk_analysis_item = @risk_analysis.risk_analysis_items.new(risk_analysis_item_params)
        if @risk_analysis_item.save
          head :no_content
        else
          render json: { errors: @risk_analysis_item.errors.messages }, status: :unprocessable_entity
        end
      end

      def update_risk_analysis_item
        @risk_analysis_item = @risk_analysis.risk_analysis_items.find(params[:item_id])
        if @risk_analysis_item.update(risk_analysis_item_params)
          head :no_content
        else
          render json: { errors: @risk_analysis_item.errors.messages }, status: :unprocessable_entity
        end
      end

      def destroy_risk_analysis_item
        @risk_analysis_item = @risk_analysis.risk_analysis_items.find(params[:item_id])
        if @risk_analysis_item.present? && @risk_analysis_item.destroy
          head :no_content
        else
          head :not_found
        end
      end

      private

      def set_risk_analysis
        @risk_analysis = RiskAnalysis.find(params[:id])
      rescue ActiveRecord::RecordNotFound
        render json: { errors: ['Risk analysis not found'] }, status: :not_found
      end

      def risk_analysis_params
        params.require(:risk_analysis).permit(:name, :company_id, :registry_activity_id, :creation_date)
      end

      def risk_analysis_item_params
        params.require(:risk_analysis_item).permit(:property, :danger, :vulnerability_description, :security, :probability,
                                                   :effect, :data_processing_impact_assessment)
      end

      def authorize_risk_analysis
        authorize @risk_analysis || RiskAnalysis
      end

      def search(options = {})
        ::Searches::RiskAnalysisSearch.new(search_options(options))
      end
    end
  end
end
