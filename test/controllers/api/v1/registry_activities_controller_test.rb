require "test_helper"

class Api::V1::RegistryActivitiesControllerTest < ActionController::TestCase
  let(:admin_user) { users(:mkalita_user) }
  let(:regular_user) { users(:milosz) }
  let(:registry_activity) { registry_activities(:one) }
  let(:closed_registry_activity) { registry_activities(:two) }
  let(:valid_params) do
    {
      registry_activity: {
        description: "Test registry activity",
        activity: "Processing personal data for test purposes",
        administrator_type: "artegence",
        co_administrator_type: "efigence",
        data_processing_goals: "Testing purposes",
        legal_basis: "Consent of the data subject",
        subjects_categories: "Test users",
        personal_data_categories: "Test data",
        categories_of_recipients: "Test recipients",
        transfer_to_third_country: "No transfer",
        planned_deletion_dates: "After test completion",
        security_measures: "Test security measures",
        creation_date: Date.today.to_s
      }
    }
  end
  let(:invalid_params) do
    {
      registry_activity: {
        description: "Test registry activity",
        activity: nil,
        administrator_type: "artegence",
        co_administrator_type: "efigence"
      }
    }
  end
  let(:update_params) do
    {
      id: registry_activity.id,
      registry_activity: {
        description: "Updated registry activity"
      }
    }
  end
  let(:invalid_update_params) do
    {
      id: registry_activity.id,
      registry_activity: {
        description: nil
      }
    }
  end

  setup do
    authenticate(admin_user)
  end

  test "returns registry activities for admin user - index" do
    get :index, format: :json

    keys = json_body.first.keys
    assert_response :success
    assert_equal RegistryActivity.count, json_body.size
    assert_includes keys, "id"
    assert_includes keys, "description"
    assert_includes keys, "administrator_type"
    assert_includes keys, "administrator_type_custom"
    assert_includes keys, "creation_date"
    assert_includes keys, "planned_deletion_dates"
    assert_includes keys, "expiration_date"
  end

  test "returns forbidden for regular user - index" do
    authenticate(regular_user)
    get :index, format: :json

    assert_response :forbidden
  end

  test "supports collection_for_select variant" do
    get :index, params: { f: { collection_for_select: 'true' } }, format: :json

    keys = json_body.first.keys
    assert_response :success
    assert_equal RegistryActivity.count, json_body.size
    assert_includes keys, "id"
    assert_includes keys, "description"
    assert_not_includes keys, "administrator_type"
    assert_not_includes keys, "planned_deletion_dates"
  end

  test "returns registry activity for admin user - show" do
    get :show, params: { id: registry_activity.id }, format: :json

    assert_response :success
    assert_equal registry_activity.id, json_body["registry_activity"]["id"]
  end

  test "returns not_found for regular user - show" do
    authenticate(regular_user)
    get :show, params: { id: registry_activity.id }, format: :json

    assert_response :not_found
  end

  test "creates registry activity for admin user - create" do
    assert_difference('RegistryActivity.count', 1) do
      post :create, params: valid_params, format: :json
    end

    assert_response :success
    assert_equal "Test registry activity", json_body['description']
  end

  test "returns forbidden for regular user - create" do
    authenticate(regular_user)

    assert_no_difference('RegistryActivity.count') do
      post :create, params: valid_params, format: :json
    end

    assert_response :forbidden
  end

  test "updates registry activity for admin user - update" do
    patch :update, params: update_params, format: :json

    assert_response :success
    assert_equal "Updated registry activity", registry_activity.reload.description
  end

  test "returns not_found for regular user - update" do
    authenticate(regular_user)
    patch :update, params: update_params, format: :json

    assert_response :not_found
    assert_not_equal "Updated registry activity", registry_activity.reload.description
  end

  test "destroys registry activity for admin user - destroy" do
    assert_difference('RegistryActivity.count', -1) do
      delete :destroy, params: { id: registry_activity.id }, format: :json
    end

    assert_response :success
  end

  test "returns not_found for regular user - destroy" do
    authenticate(regular_user)

    assert_no_difference('RegistryActivity.count') do
      delete :destroy, params: { id: registry_activity.id }, format: :json
    end

    assert_response :not_found
  end

  test "returns new registry activity form data for admin user - new" do
    get :new, format: :json

    assert_response :success
    assert_includes json_body.keys, "administrator_types"
    assert_includes json_body.keys, "co_administrator_types"
  end

  test "returns forbidden for regular user - new" do
    authenticate(regular_user)
    get :new, format: :json

    assert_response :forbidden
  end

  test "activates closed registry activity for admin user - activate" do
    patch :activate, params: { id: closed_registry_activity.id }, format: :json

    assert_response :success
    assert closed_registry_activity.reload.active?
  end

  test "returns not_found for regular user - activate" do
    authenticate(regular_user)
    patch :activate, params: { id: closed_registry_activity.id }, format: :json

    assert_response :not_found
    assert closed_registry_activity.reload.closed?
  end

  test "closes active registry activity for admin user - close" do
    patch :close, params: { id: registry_activity.id }, format: :json

    assert_response :success
    assert registry_activity.reload.closed?
  end

  test "returns not_found for regular user - close" do
    authenticate(regular_user)
    patch :close, params: { id: registry_activity.id }, format: :json

    assert_response :not_found
    assert registry_activity.reload.active?
  end

  test "returns error for create with invalid parameters" do
    assert_no_difference('RegistryActivity.count') do
      post :create, params: invalid_params, format: :json
    end

    assert_response :unprocessable_entity
    assert_includes json_body["errors"].keys, "activity"
  end

  test "returns error for update with invalid parameters" do
    patch :update, params: invalid_update_params, format: :json

    assert_response :unprocessable_entity
    assert_includes json_body["errors"].keys, "description"
  end

  test "returns not found for non-existent record" do
    get :show, params: { id: 999999 }, format: :json

    assert_response :not_found
  end

  test "cannot activate an already active registry activity" do
    patch :activate, params: { id: registry_activity.id }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body["errors"]
  end

  test "cannot close an already closed registry activity" do
    patch :close, params: { id: closed_registry_activity.id }, format: :json

    assert_response :unprocessable_entity
    assert_not_empty json_body["errors"]
  end
end
